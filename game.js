// 游戏初始化
let snake = [{x: 10, y: 10}];
let food = {x: Math.floor(Math.random() * 20), y: Math.floor(Math.random() * 20)};
let direction = 'right';
let gameOver = false;

// 处理键盘事件
document.addEventListener('keydown', (event) => {
  switch (event.key) {
    case 'w':
    case 'ArrowUp':
      if (direction !== 'down') direction = 'up';
      break;
    case 's':
    case 'ArrowDown':
      if (direction !== 'up') direction = 'down';
      break;
    case 'a':
    case 'ArrowLeft':
      if (direction !== 'right') direction = 'left';
      break;
    case 'd':
    case 'ArrowRight':
      if (direction !== 'left') direction = 'right';
      break;
  }
});

// 游戏循环
function gameLoop() {
  if (gameOver) return;

  // 移动蛇
  let newHead = {...snake[0]};
  switch (direction) {
    case 'up':
      newHead.y--;
      break;
    case 'down':
      newHead.y++;
      break;
    case 'left':
      newHead.x--;
      break;
    case 'right':
      newHead.x++;
      break;
  }
  snake.unshift(newHead);
  if (newHead.x === food.x && newHead.y === food.y) {
    food = {x: Math.floor(Math.random() * 20), y: Math.floor(Math.random() * 20)};
  } else {
    snake.pop();
  }

  // 检查碰撞
  if (snake.some((segment, index) => index !== 0 && segment.x === newHead.x && segment.y === newHead.y) ||
      newHead.x < 0 || newHead.x >= 20 || newHead.y < 0 || newHead.y >= 20) {
    gameOver = true;
  }

  // 渲染游戏
  // ...

  requestAnimationFrame(gameLoop);
}

gameLoop();